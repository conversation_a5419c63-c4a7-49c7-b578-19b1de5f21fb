# Element UI 表单组件规则配置文档

## 概述

本文档描述了 `packages/element-ui/src/config/rule/` 目录下所有表单组件规则的结构和配置模式，用于指导 AI 生成新的组件规则代码。这些规则配置最终会被转换为表单 JSON 配置，用于动态生成表单组件。

## 最终输出格式示例

规则配置最终会生成如下格式的 JSON 数据：

```json
[
  {
    "type": "input",
    "field": "F3lsme5ab488acc",
    "title": "输入框",
    "$required": false,
    "_fc_id": "id_Fa1ime5ab488adc",
    "name": "ref_Ftyeme5ab488aec",
    "display": true,
    "hidden": false,
    "_fc_drag_tag": "input"
  },
  {
    "type": "input",
    "field": "F85vme5ab4ogahc",
    "title": "多行输入框",
    "$required": false,
    "props": {
      "type": "textarea"
    },
    "_fc_id": "id_F4vqme5ab4ogaic",
    "name": "ref_F3sxme5ab4ogajc",
    "display": true,
    "hidden": false,
    "_fc_drag_tag": "textarea"
  },
  {
    "type": "input",
    "field": "Fenyme5ab59tamc",
    "title": "密码输入框",
    "$required": false,
    "props": {
      "type": "password"
    },
    "_fc_id": "id_Fky5me5ab59tanc",
    "name": "ref_Fb1nme5ab59taoc",
    "display": true,
    "hidden": false,
    "_fc_drag_tag": "password"
  }
]
```

### JSON 字段说明

- `type`: 组件类型，对应规则文件中的 `name` 字段
- `field`: 唯一字段标识符，由 `uniqueId()` 生成
- `title`: 组件标题，来自规则的 `title` 配置
- `$required`: 是否必填，来自规则的 `$required` 配置
- `props`: 组件属性对象，来自规则的 `props` 配置
- `_fc_id`: 内部唯一ID，由系统自动生成
- `name`: 组件引用名称，由系统自动生成
- `display`: 是否显示，默认为 true
- `hidden`: 是否隐藏，默认为 false
- `_fc_drag_tag`: 拖拽标签，通常与组件类型相关

## 文件列表

该目录包含以下 44 个组件规则文件：

### 表单输入组件
- `input.js` - 输入框
- `textarea.js` - 文本域
- `password.js` - 密码输入框
- `number.js` - 数字输入框
- `select.js` - 选择器
- `cascader.js` - 级联选择器
- `checkbox.js` - 复选框
- `radio.js` - 单选框
- `switch.js` - 开关
- `slider.js` - 滑块
- `rate.js` - 评分
- `color.js` - 颜色选择器
- `date.js` - 日期选择器
- `dateRange.js` - 日期范围选择器
- `time.js` - 时间选择器
- `timeRange.js` - 时间范围选择器
- `upload.js` - 文件上传
- `transfer.js` - 穿梭框
- `tree.js` - 树形控件
- `treeSelect.js` - 树形选择器

### 布局组件
- `row.js` - 行布局
- `col.js` - 列布局
- `card.js` - 卡片
- `collapse.js` - 折叠面板
- `collapseItem.js` - 折叠面板项
- `tabs.js` - 标签页
- `tabPane.js` - 标签页面板
- `table.js` - 表格布局
- `space.js` - 间距

### 表单容器组件
- `group.js` - 分组
- `subForm.js` - 子表单
- `tableForm.js` - 表格表单
- `tableFormColumn.js` - 表格表单列

### 辅助组件
- `button.js` - 按钮
- `alert.js` - 警告提示
- `tag.js` - 标签
- `text.js` - 文本
- `title.js` - 标题
- `html.js` - HTML
- `image.js` - 图片
- `divider.js` - 分割线
- `editor.js` - 富文本编辑器
- `signaturePad.js` - 签名板

## 规则文件结构

每个规则文件都遵循统一的结构模式：

### 基本结构

```javascript
import uniqueId from '@form-create/utils/lib/unique';
import {localeProps, localeOptions, getInjectArg} from '../../utils';

const label = '组件中文名称';
const name = '组件英文名称';

export default {
    // 基本配置
    menu: 'main|layout|aide',           // 菜单分类
    icon: 'icon-组件名',                 // 图标类名
    label,                              // 显示标签
    name,                               // 组件名称
    
    // 可选配置
    input: true,                        // 是否为输入组件
    drag: true,                         // 是否可拖拽
    inside: false,                      // 是否为内部组件
    mask: false,                        // 是否显示遮罩
    
    // 事件配置
    event: ['change', 'blur', 'focus'], // 支持的事件列表
    
    // 验证配置
    validate: ['string', 'number'],      // 支持的验证规则
    
    // 国际化配置
    languageKey: ['key1', 'key2'],      // 需要国际化的键
    
    // 规则生成函数
    rule({t}) {
        return {
            type: name,
            field: uniqueId(),          // 唯一字段名（输入组件）
            title: t('com.组件名.name'), // 组件标题
            info: '',                   // 组件说明
            $required: false,           // 是否必填
            props: {},                  // 组件属性
            style: {},                  // 样式配置
            children: [],               // 子组件（容器组件）
            options: [],                // 选项配置（选择类组件）
            effect: {}                  // 副作用配置
        };
    },
    
    // 监听器配置
    watch: {
        propertyName({rule}) {
            // 属性变化时的处理逻辑
        }
    },
    
    // 属性配置函数
    props(_, {t}) {
        return localeProps(t, name + '.props', [
            // 属性配置数组
        ]);
    }
};
```

### 菜单分类

- `main` - 主要表单组件（输入类组件）
- `layout` - 布局组件
- `aide` - 辅助组件

### 组件类型特征

#### 输入组件特征
- 设置 `input: true`
- 包含 `field: uniqueId()` 生成唯一字段名
- 包含 `$required: false` 必填配置
- 通常有 `validate` 验证规则配置
- 常见事件：`['change', 'blur', 'focus', 'input', 'clear']`

#### 布局组件特征
- 设置 `menu: 'layout'`
- 包含 `children: []` 子组件配置
- 可能设置 `drag: true` 支持拖拽
- 可能设置 `inside: false` 和 `mask: false`

#### 辅助组件特征
- 设置 `menu: 'aide'`
- 可能设置 `mask: true` 显示遮罩
- 通常不包含 `field` 字段

### 属性配置模式

属性配置使用 `localeProps` 函数包装，支持以下类型：

#### 基础输入类型
```javascript
{type: 'input', field: '属性名'}                    // 文本输入
{type: 'textarea', field: '属性名'}                 // 文本域
{type: 'inputNumber', field: '属性名', props: {min: 0}} // 数字输入
{type: 'switch', field: '属性名'}                   // 开关
```

#### 选择类型
```javascript
{
    type: 'select',
    field: '属性名',
    options: localeOptions(t, [
        {label: '显示文本', value: '值'},
        // ...更多选项
    ])
}
```

#### 特殊类型
```javascript
{type: 'ColorInput', field: '属性名'}               // 颜色选择器
{type: 'TableOptions', field: '属性名', props: {}}  // 表格选项
{type: 'FnInput', field: '属性名', props: {}}       // 函数输入
```

### 函数输入配置

对于需要函数配置的属性，使用 `FnInput` 类型：

```javascript
{
    type: 'FnInput',
    field: '函数名',
    props: {
        body: true,                     // 显示函数体
        button: true,                   // 显示按钮
        fnx: true,                      // 支持 FNX 语法
        args: [getInjectArg(t)],       // 函数参数
        name: '函数名',
    }
}
```

### 选项配置

对于有选项的组件（如 select、radio、checkbox），使用：

```javascript
options: makeTreeOptions(t('props.option'), {label: 'label', value: 'value'}, 1)
```

或在 props 中使用：

```javascript
makeOptionsRule(t, 'options')
```

## 国际化支持

所有文本内容都通过 `t()` 函数进行国际化处理：

- 组件标题：`t('com.组件名.name')`
- 属性标签：通过 `localeProps` 自动处理
- 选项标签：通过 `localeOptions` 处理

## 规则配置到 JSON 的转换关系

### 输入组件转换示例

以 `input.js` 规则为例：

**规则配置：**
```javascript
rule({t}) {
    return {
        type: 'input',              // → JSON 中的 type
        field: uniqueId(),          // → JSON 中的 field
        title: t('com.input.name'), // → JSON 中的 title
        $required: false,           // → JSON 中的 $required
        props: {}                   // → JSON 中的 props
    };
}
```

**生成的 JSON：**
```json
{
    "type": "input",
    "field": "F3lsme5ab488acc",
    "title": "输入框",
    "$required": false,
    "props": {},
    "_fc_id": "id_Fa1ime5ab488adc",
    "name": "ref_Ftyeme5ab488aec",
    "display": true,
    "hidden": false,
    "_fc_drag_tag": "input"
}
```

### 不同组件类型的 props 配置

#### 1. 基础输入框（type: "input"）
```json
{
    "type": "input",
    "props": {}  // 默认为文本输入
}
```

#### 2. 多行文本框（type: "input" + props.type: "textarea"）
```json
{
    "type": "input",
    "props": {
        "type": "textarea"
    }
}
```

#### 3. 密码输入框（type: "input" + props.type: "password"）
```json
{
    "type": "input",
    "props": {
        "type": "password"
    }
}
```

#### 4. 选择器组件
```json
{
    "type": "select",
    "props": {
        "multiple": false,
        "clearable": true
    },
    "options": [
        {"label": "选项1", "value": "1"},
        {"label": "选项2", "value": "2"}
    ]
}
```

#### 5. 布局组件
```json
{
    "type": "elCard",
    "props": {
        "header": "卡片标题"
    },
    "style": {
        "width": "100%"
    },
    "children": []
}
```

## 代码生成指南

基于此文档，AI 可以按照以下步骤生成新的组件规则：

### 第一步：分析需求
1. 确定组件类型（输入/布局/辅助）
2. 确定最终 JSON 输出格式
3. 分析需要的 props 配置

### 第二步：创建规则文件
1. 选择合适的菜单分类（main/layout/aide）
2. 配置基本属性（name, label, icon等）
3. 根据组件类型设置特征属性
4. 实现 rule() 函数生成组件规则
5. 实现 props() 函数配置组件属性
6. 添加必要的事件和验证配置
7. 处理国际化文本

### 第三步：验证输出
确保生成的规则能够产生期望的 JSON 格式，包括：
- 正确的 type 字段
- 合适的 props 配置
- 必要的 field 和 title 设置
- 适当的验证和事件配置

### 组件命名规范

- **文件名**: 使用小写字母和连字符，如 `date-range.js`
- **组件名**: 使用驼峰命名，如 `dateRange`
- **Element UI 组件**: 使用 `el` 前缀，如 `elButton`、`elCard`
- **自定义组件**: 使用 `fc` 前缀，如 `fcTable`

每个新组件都应该遵循现有的命名约定和结构模式，确保与整个系统的一致性，并能正确生成目标 JSON 格式。
